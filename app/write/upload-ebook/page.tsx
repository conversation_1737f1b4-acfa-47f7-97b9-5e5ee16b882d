"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { enhancedStorage, projectCoverStorage } from "@/lib/supabase/storage"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import ProcessingStatus from "@/components/ProcessingStatus"
import { EbookReader } from "@/components/EbookReader"

export default function UploadEbookPage() {
  const router = useRouter()
  const [userProfile, setUserProfile] = useState<{ name: string } | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    authorName: '',
    description: '',
    genre: '',
    keywords: '',
    priceAmount: '',
    isbn: '',
    publicationDate: '',
    metaDescription: '',
    slug: ''
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [coverPreview, setCoverPreview] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [coverDragActive, setCoverDragActive] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewChapters, setPreviewChapters] = useState<any[]>([])
  const [processingPreview, setProcessingPreview] = useState(false)
  const [fileType, setFileType] = useState<'epub' | 'docx' | 'doc' | 'rtf'>('epub')
  const supabase = createSupabaseClient()

  const handlePreview = useCallback(async () => {
    if (!selectedFile) return

    console.log('Starting preview processing for file:', selectedFile.name)
    setProcessingPreview(true)

    try {
      // Create FormData to send the file to the server
      const formData = new FormData()
      formData.append('file', selectedFile)

      console.log('Sending request to /api/preview-ebook')

      // Send to server-side processing endpoint
      const response = await fetch('/api/preview-ebook', {
        method: 'POST',
        body: formData
      }).catch(fetchError => {
        console.error('Fetch error:', fetchError)
        throw new Error(`Network error: ${fetchError.message}`)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Server error response:', errorText)
        throw new Error(`Server error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Unknown error occurred')
      }

      console.log('Preview processing result:', data)

      if (data.chapters && data.chapters.length > 0) {
        setPreviewChapters(data.chapters)
        setShowPreview(true)
      } else {
        alert('No chapters could be extracted from this file. Please check that it\'s a valid EPUB.')
      }

    } catch (error) {
      console.error('Error processing file for preview:', error)
      alert(`Failed to process file: ${error.message}`)
    } finally {
      setProcessingPreview(false)
    }
  }, [selectedFile])

  // Check for update-preview mode and load file from sessionStorage
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const mode = urlParams.get('mode')

    if (mode === 'update-preview') {
      const storedFileData = sessionStorage.getItem('fileForPreview')
      if (storedFileData) {
        try {
          const fileData = JSON.parse(storedFileData)

          // Convert base64 back to File
          fetch(fileData.data)
            .then(res => res.blob())
            .then(blob => {
              const file = new File([blob], fileData.name, { type: fileData.type })
              setSelectedFile(file)
            })
            .catch(error => {
              console.error('Error loading file from storage:', error)
            })

          // Clear the stored data
          sessionStorage.removeItem('fileForPreview')
        } catch (error) {
          console.error('Error loading file from storage:', error)
        }
      }
    }
  }, [])

  // Auto-trigger preview when file is loaded in update-preview mode
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const mode = urlParams.get('mode')

    if (mode === 'update-preview' && selectedFile) {
      // Small delay to ensure the file is properly set
      const timer = setTimeout(() => {
        handlePreview()
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [selectedFile, handlePreview])

  // Fetch user profile on component mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          const { data: profile } = await supabase
            .from('users')
            .select('name')
            .eq('id', user.id)
            .single()

          if (profile) {
            setUserProfile(profile)
          }
        }
      } catch (error) {
        console.error('Error fetching user profile:', error)
      }
    }

    fetchUserProfile()
  }, [])



  // Comprehensive genre list like Amazon
  const genres = [
    'Action & Adventure', 'African American Fiction', 'African American Romance', 'Alternate History',
    'Anthology', 'Biographies & Memoirs', 'Business & Money', 'Children\'s Books', 'Christian Fiction',
    'Comics & Graphic Novels', 'Contemporary Fiction', 'Contemporary Romance', 'Cookbooks',
    'Crime & Mystery', 'Dystopian', 'Education & Teaching', 'Epic Fantasy', 'Erotica', 'Fantasy',
    'Health & Fitness', 'Historical Fiction', 'Historical Romance', 'History', 'Hood Fiction',
    'Horror', 'Humor & Entertainment', 'Interracial Romance', 'LGBTQ+', 'Literary Fiction',
    'Medical Thrillers', 'Memoirs', 'Military Fiction', 'Music', 'Mystery', 'New Adult',
    'Non-Fiction', 'Paranormal Romance', 'Parenting & Relationships', 'Philosophy', 'Poetry',
    'Political Thrillers', 'Politics & Social Sciences', 'Psychology', 'Religion & Spirituality',
    'Romance', 'Science & Math', 'Science Fiction', 'Self-Help', 'Short Stories',
    'Sports & Outdoors', 'Street Fiction', 'Suspense', 'Technology', 'Teen & Young Adult',
    'Thrillers', 'Travel', 'True Crime', 'Urban Contemporary', 'Urban Drama', 'Urban Fantasy',
    'Urban Fiction', 'Urban Romance', 'War & Military', 'Western', 'Women\'s Fiction'
  ]

  // Calculate earnings breakdown
  const calculateEarnings = (price: string) => {
    const priceNum = parseFloat(price) || 0
    if (priceNum === 0) return { gross: 0, stripeFee: 0, platformFee: 0, authorEarnings: 0 }

    const grossCents = Math.round(priceNum * 100)
    const stripeFee = Math.round(grossCents * 0.029 + 30) // 2.9% + 30¢
    const afterStripe = grossCents - stripeFee
    const platformFee = Math.round(afterStripe * 0.20) // 20% platform fee
    const authorEarnings = afterStripe - platformFee

    return {
      gross: grossCents / 100,
      stripeFee: stripeFee / 100,
      platformFee: platformFee / 100,
      authorEarnings: authorEarnings / 100
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Auto-generate slug from title
      ...(field === 'title' && { slug: generateSlug(value) })
    }))
  }

  const handleFileSelect = (file: File) => {
    // Validate file type - check both MIME type and file extension
    const fileName = file.name.toLowerCase()
    const fileExtension = fileName.split('.').pop()

    const allowedMimeTypes = [
      'application/epub+zip',
      'application/epub',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/rtf', // .rtf
      'text/rtf', // alternative RTF MIME type
      'application/octet-stream' // Generic binary type that some systems use
    ]

    const allowedExtensions = ['epub', 'docx', 'doc', 'rtf', 'zip'] // Allow zip as it might be an EPUB

    // File is valid if it has the right MIME type OR the right extension
    const hasValidMimeType = allowedMimeTypes.includes(file.type)
    const hasValidExtension = allowedExtensions.includes(fileExtension || '')

    // Accept file if it has valid extension (primary check)
    if (!hasValidExtension) {
      alert(`Please select an EPUB or document file (DOCX, DOC, RTF). Selected file has extension: ${fileExtension}`)
      return
    }

    // Special handling for ZIP files - warn user but allow upload
    if (fileExtension === 'zip') {
      const confirmUpload = confirm(
        'You selected a ZIP file. EPUB files should have .epub extension.\n\n' +
        'If this is an EPUB file with wrong extension, click OK to continue.\n' +
        'If this is just a regular ZIP file, click Cancel and select a proper EPUB or document file.'
      )
      if (!confirmUpload) {
        return
      }
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      alert('File size must be less than 50MB')
      return
    }

    setSelectedFile(file)

    // Set file type based on extension (reuse the already declared fileExtension)
    if (fileExtension === 'docx') {
      setFileType('docx')
    } else if (fileExtension === 'doc') {
      setFileType('doc')
    } else if (fileExtension === 'rtf') {
      setFileType('rtf')
    } else {
      setFileType('epub')
    }
  }

  const handleCoverSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file for the cover')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      alert('Cover image must be less than 5MB')
      return
    }

    setCoverFile(file)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setCoverPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleCoverDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setCoverDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleCoverSelect(files[0])
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }





  const handleSubmit = async (e?: React.FormEvent): Promise<string | null> => {
    e?.preventDefault()

    if (!formData.title.trim() || !selectedFile || !coverFile) {
      alert('Please provide a title, select an ebook file, and upload a book cover')
      return
    }

    // Enhanced duplicate checking with better logic
    try {
      const { data: existingBooks } = await supabase
        .from('projects')
        .select('id, title, author_name, ebook_file_type, status, total_chapters, created_at')
        .eq('title', formData.title.trim())
        .eq('author_name', formData.authorName?.trim() || userProfile?.name || '')
        .eq('is_ebook', true)
        .neq('status', 'archived') // Don't count archived books

      if (existingBooks && existingBooks.length > 0) {
        const existingBook = existingBooks[0]
        const currentFileType = fileType

        // Check if it's the same file type
        if (existingBook.ebook_file_type === currentFileType) {
          // Same file type - check if it's a failed processing or complete book
          if (existingBook.status === 'processing' && existingBook.total_chapters === 0) {
            const shouldReplace = window.confirm(
              `A ${currentFileType.toUpperCase()} version of "${formData.title}" exists but failed to process properly. ` +
              `Do you want to replace it with this new upload?`
            )
            if (shouldReplace) {
              // Delete the failed book and continue
              await supabase.from('projects').delete().eq('id', existingBook.id)
            } else {
              return
            }
          } else {
            // Complete book exists
            alert(`"${formData.title}" by ${formData.authorName || userProfile?.name || 'this author'} already exists and is ${existingBook.status}. Please use a different title or delete the existing book first.`)
            return
          }
        } else {
          // Different file type - ask user if they want to replace
          const shouldReplace = window.confirm(
            `A ${existingBook.ebook_file_type?.toUpperCase()} version of "${formData.title}" by ${formData.authorName || userProfile?.name || 'this author'} already exists (${existingBook.status}). ` +
            `Do you want to replace it with this ${currentFileType.toUpperCase()} version?`
          )

          if (shouldReplace) {
            // Archive the existing book instead of deleting (preserve data)
            await supabase
              .from('projects')
              .update({ status: 'archived' })
              .eq('id', existingBook.id)
          } else {
            return
          }
        }
      }
    } catch (error) {
      console.error('Error checking for existing books:', error)
      // Continue with upload if check fails
    }

    setUploading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth/login')
        return
      }

      // Upload ebook file
      console.log('Starting ebook file upload...')
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `${Date.now()}-${formData.slug}.${fileExt}`

      const { data: fileUpload, error: fileError } = await enhancedStorage.upload(
        'ebooks',
        fileName,
        selectedFile,
        {
          cacheControl: '3600',
          upsert: false
        }
      )

      if (fileError) {
        console.error('Ebook file upload error:', fileError)
        throw new Error(`File upload failed: ${fileError.message}`)
      }
      console.log('Ebook file uploaded successfully:', fileName)

      const { data: { publicUrl: ebookUrl } } = enhancedStorage.getPublicUrl('ebooks', fileName)

      // Upload cover (now mandatory)
      console.log('Starting cover upload...')
      const coverExt = coverFile.name.split('.').pop()
      const coverFileName = `${Date.now()}-${formData.slug}-cover.${coverExt}`

      const { data: coverUpload, error: coverError } = await projectCoverStorage.upload(
        coverFileName,
        coverFile,
        {
          cacheControl: '3600',
          upsert: false
        }
      )

      if (coverError) {
        console.error('Cover upload error:', coverError)
        throw new Error(`Cover upload failed: ${coverError.message}`)
      }
      console.log('Cover uploaded successfully:', coverFileName)

      const { data: { publicUrl: coverUrl } } = projectCoverStorage.getPublicUrl(coverFileName)

      // Create project record
      console.log('Creating project record...')
      const priceInCents = formData.priceAmount ? Math.round(parseFloat(formData.priceAmount) * 100) : 0
      const keywordsArray = formData.keywords ? formData.keywords.split(',').map(keyword => keyword.trim()).filter(Boolean) : []

      // Ensure slug is properly formatted and unique
      let baseSlug = formData.slug.trim().toLowerCase().replace(/[^a-z0-9-]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
      if (!baseSlug) {
        throw new Error('Invalid slug format')
      }

      // Check for existing slugs and make it unique
      let finalSlug = baseSlug
      let slugCounter = 1

      while (true) {
        const { data: existingProject } = await supabase
          .from('projects')
          .select('id')
          .eq('slug', finalSlug)
          .single()

        if (!existingProject) {
          break // Slug is unique
        }

        finalSlug = `${baseSlug}-${slugCounter}`
        slugCounter++

        // Prevent infinite loop
        if (slugCounter > 100) {
          throw new Error('Unable to generate unique slug')
        }
      }

      console.log('Using unique slug:', finalSlug)

      const projectData = {
        user_id: user.id,
        title: formData.title.trim(),
        author_name: formData.authorName.trim() || userProfile?.name || null,
        description: formData.description.trim() || null,
        genre: formData.genre.trim() || null,
        tags: keywordsArray,
        price_amount: priceInCents,
        preview_chapters: 1, // Only allow chapter 1
        isbn: formData.isbn.trim() || null,
        publication_date: formData.publicationDate || null,
        meta_description: formData.metaDescription.trim() || null,
        slug: finalSlug,
        is_ebook: true,
        is_complete: false, // Will be set to true after processing
        status: 'processing', // Track the processing state
        ebook_file_url: ebookUrl,
        ebook_file_type: fileExt as 'epub' | 'docx' | 'doc' | 'rtf',
        cover_image_url: coverUrl,
        social_image_url: coverUrl // Use cover as social image
      }

      console.log('Project data to insert:', projectData)

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert(projectData)
        .select('id, slug, title')
        .single()

      if (projectError) {
        console.error('Project creation error:', projectError)
        throw new Error(`Project creation failed: ${projectError.message}`)
      }
      console.log('Project created successfully:', {
        id: project.id,
        slug: project.slug,
        title: project.title
      })

      // Start processing with visual feedback
      setProcessing(true)
      console.log(`Starting ${fileExt} processing...`)

      try {
        // Process all supported file types for accurate word counting
        const response = await fetch('/api/process-ebook', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            projectId: project.id,
            fileUrl: ebookUrl,
            fileType: fileExt
          })
        })

        const result = await response.json()
        if (response.ok) {
          console.log('Ebook processing completed:', result.data)
          // Processing status component will handle the success message
        } else {
          console.error('Ebook processing failed:', result.error)
          alert(`⚠️ Upload successful but processing had issues:\n${result.error}\n\nYou can manually update the book details later.`)
        }
      } catch (processingError) {
        console.error('Ebook processing error:', processingError)
        alert('⚠️ Upload successful but processing failed. You can manually update the book details later.')
      } finally {
        setProcessing(false)
      }

      // Success - redirect to book page
      router.push(`/books/${project.slug || project.id}`)
      return project.id
    } catch (error) {
      console.error('Error uploading ebook:', error)
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        stack: error?.stack
      })

      let errorMessage = 'Failed to upload ebook. Please try again.'
      if (error?.message) {
        errorMessage = `Upload failed: ${error.message}`
      }

      alert(errorMessage)
      return null
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-3 sm:px-4 py-4 sm:py-8">

        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-3 sm:mb-4 px-2">
            ✨ Publish Your Masterpiece
          </h1>
          <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-2">
            Transform your completed book into a published work. Upload your ebook and cover to join our curated store where readers discover their next favorite read.
          </p>
          <div className="mt-3 sm:mt-4 inline-flex items-center px-3 sm:px-4 py-2 bg-green-100 text-green-800 rounded-full text-xs sm:text-sm font-medium">
            💰 You keep 80% of all sales after platform fees
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-8">

          {/* Book Cover Upload - First and Mandatory */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-serif text-gray-900 mb-3 sm:mb-4">📸 Book Cover *</h2>
              <p className="text-sm sm:text-base text-gray-600 mb-3 sm:mb-4">
                A professional book cover is mandatory for store listing. Recommended 10:16 aspect ratio for best results.
              </p>

              <div
                onDrop={handleCoverDrop}
                onDragOver={(e) => { e.preventDefault(); setCoverDragActive(true) }}
                onDragLeave={() => setCoverDragActive(false)}
                className={`border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-colors ${
                  coverDragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                }`}
              >
                {coverPreview ? (
                  <div className="flex flex-col items-center">
                    <div className="w-32 sm:w-40 h-48 sm:h-60 bg-gray-100 rounded-lg shadow-md mb-3 sm:mb-4 overflow-hidden">
                      <img
                        src={coverPreview}
                        alt="Cover preview"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <p className="font-medium text-gray-900 mb-1 sm:mb-2 text-sm sm:text-base truncate max-w-full px-2">{coverFile?.name}</p>
                    <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">
                      {coverFile ? (coverFile.size / (1024 * 1024)).toFixed(2) : 0} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setCoverFile(null)
                        setCoverPreview(null)
                      }}
                      className="w-full sm:w-auto text-red-600 border-red-200 hover:bg-red-50"
                    >
                      🗑️ Remove Cover
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="text-3xl sm:text-4xl mb-2">📸</div>
                    <p className="text-sm sm:text-base text-gray-600 mb-2 px-2">
                      Tap to upload your book cover
                    </p>
                    <p className="text-xs text-gray-500 mb-4 px-2">
                      Recommended: 10:16 aspect ratio • Max size: 5MB • JPG, PNG, WebP
                    </p>

                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleCoverSelect(file)
                      }}
                      className="hidden"
                      id="cover-upload"
                    />
                    <label
                      htmlFor="cover-upload"
                      className="bg-purple-600 text-white px-4 sm:px-6 py-3 sm:py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block w-full sm:w-auto text-center text-sm sm:text-base font-medium"
                    >
                      📁 Choose Cover Image
                    </label>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Upload Section */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-serif text-gray-900 mb-3 sm:mb-4">📖 Book File *</h2>

              <div
                onDrop={handleDrop}
                onDragOver={(e) => { e.preventDefault(); setDragActive(true) }}
                onDragLeave={() => setDragActive(false)}
                className={`border-2 border-dashed rounded-lg p-4 sm:p-8 text-center transition-colors ${
                  dragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
                }`}
              >
                {selectedFile ? (
                  <div>
                    <div className="text-3xl sm:text-4xl mb-2">📄</div>
                    <p className="font-medium text-gray-900 text-sm sm:text-base truncate max-w-full px-2">{selectedFile.name}</p>
                    <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">
                      {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setSelectedFile(null)}
                      className="mt-2 w-full sm:w-auto text-red-600 border-red-200 hover:bg-red-50"
                    >
                      🗑️ Remove File
                    </Button>
                  </div>
                ) : (
                  <div>
                    <div className="text-3xl sm:text-4xl mb-2">📚</div>
                    <p className="text-sm sm:text-base text-gray-600 mb-2 px-2">
                      Tap to upload your ebook file
                    </p>
                    <p className="text-xs text-gray-500 mb-4 px-2">
                      Supported formats: EPUB, DOCX, DOC, RTF • Max size: 50MB
                    </p>

                    <input
                      type="file"
                      accept=".epub,.docx,.doc,.rtf,.zip,application/epub+zip,application/epub,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,application/rtf,text/rtf,application/zip"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleFileSelect(file)
                      }}
                      className="hidden"
                      id="ebook-upload"
                    />
                    <label
                      htmlFor="ebook-upload"
                      className="bg-purple-600 text-white px-4 sm:px-6 py-3 sm:py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block w-full sm:w-auto text-center text-sm sm:text-base font-medium"
                    >
                      📁 Choose File
                    </label>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Book Details */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-serif text-gray-900 mb-3 sm:mb-4">📝 Book Details</h2>

              <div className="space-y-4 sm:space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Book Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                    placeholder="Enter your book title"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Author Name
                  </label>
                  <input
                    type="text"
                    value={formData.authorName}
                    onChange={(e) => handleInputChange('authorName', e.target.value)}
                    className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                    placeholder={`Enter author name (leave blank to use: ${userProfile?.name || 'your profile name'})`}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Optional: Specify a different author name for this book
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Book Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-40 sm:h-32 resize-y text-base"
                    placeholder="Write a compelling description for your book. Include what readers can expect, key themes, and what makes your book unique..."
                    maxLength={4000}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.description.length}/4000 characters
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Genre *
                    </label>
                    <select
                      value={formData.genre}
                      onChange={(e) => handleInputChange('genre', e.target.value)}
                      className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                      required
                    >
                      <option value="">Select genre</option>
                      {genres.map((genre) => (
                        <option key={genre} value={genre}>{genre}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Keywords
                    </label>
                    <input
                      type="text"
                      value={formData.keywords}
                      onChange={(e) => handleInputChange('keywords', e.target.value)}
                      className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                      placeholder="romance, love story, contemporary"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Help readers discover your book with relevant keywords
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price (USD)
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-3 sm:top-2 text-gray-500 text-base">$</span>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.priceAmount}
                      onChange={(e) => handleInputChange('priceAmount', e.target.value)}
                      className="w-full pl-8 pr-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                      placeholder="0.00"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Leave empty or set to 0 for free ebook
                  </p>

                  {/* Earnings Calculator */}
                  {formData.priceAmount && parseFloat(formData.priceAmount) > 0 && (
                    <div className="mt-4 p-3 sm:p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2 text-sm sm:text-base">💰 Your Earnings Breakdown</h4>
                      {(() => {
                        const earnings = calculateEarnings(formData.priceAmount)
                        return (
                          <div className="space-y-1 text-xs sm:text-sm">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Book Price:</span>
                              <span className="font-medium">${earnings.gross.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Stripe Fee (2.9% + 30¢):</span>
                              <span className="text-red-600">-${earnings.stripeFee.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">OnlyDiary Fee (20%):</span>
                              <span className="text-red-600">-${earnings.platformFee.toFixed(2)}</span>
                            </div>
                            <div className="border-t border-green-300 pt-2 mt-2">
                              <div className="flex justify-between items-center font-bold">
                                <span className="text-green-900">You Keep (80% after fees)*:</span>
                                <span className="text-green-900 text-base">${earnings.authorEarnings.toFixed(2)}</span>
                              </div>
                            </div>
                          </div>
                        )
                      })()}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Metadata */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-serif text-gray-900 mb-3 sm:mb-4">📋 Additional Information</h2>

              <div className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ISBN (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.isbn}
                      onChange={(e) => handleInputChange('isbn', e.target.value)}
                      className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                      placeholder="978-0-123456-78-9"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      International Standard Book Number
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Publication Date
                    </label>
                    <input
                      type="date"
                      value={formData.publicationDate}
                      onChange={(e) => handleInputChange('publicationDate', e.target.value)}
                      className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Description (Optional)
                  </label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                    className="w-full px-3 py-3 sm:py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 sm:h-20 resize-none text-base"
                    placeholder="A brief description for search engines and social media..."
                    maxLength={160}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.metaDescription.length}/160 characters • Helps with discoverability
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={uploading}
              className="w-full sm:w-auto order-3 sm:order-1"
            >
              Cancel
            </Button>

            {formData.title && coverFile && selectedFile && (
              <Button
                type="button"
                variant="outline"
                onClick={handlePreview}
                disabled={uploading || processingPreview}
                className="border-purple-200 text-purple-700 hover:bg-purple-50 w-full sm:w-auto order-2"
              >
                {processingPreview ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-700 mr-2"></div>
                    Processing...
                  </>
                ) : (
                  '👀 Preview in E-Reader'
                )}
              </Button>
            )}

            <Button
              type="submit"
              isLoading={uploading}
              disabled={!formData.title.trim() || !selectedFile || !coverFile}
              className="bg-purple-600 text-white hover:bg-purple-700 w-full sm:w-auto order-1 sm:order-3"
            >
              {uploading ? 'Uploading...' : 'Publish to Store'}
            </Button>
          </div>
        </form>



        {/* E-Reader Preview */}
        {showPreview && previewChapters.length > 0 && (
          <EbookReader
            chapters={previewChapters}
            bookTitle={formData.title || 'Your Book Title'}
            authorName={formData.authorName || userProfile?.name || 'Your Name'}
            projectId="preview"
            userId="preview-user"
            isPreview={true}
            onClose={() => {
              setShowPreview(false)
              setPreviewChapters([])
            }}
            onApprovePreview={() => {
              // Close preview and trigger the actual upload/publish process
              setShowPreview(false)
              setPreviewChapters([])
              // Trigger the form submission
              const form = document.querySelector('form') as HTMLFormElement
              if (form) {
                form.requestSubmit()
              }
            }}
          />
        )}



        {/* Processing Status Modal */}
        <ProcessingStatus
          isVisible={processing}
          fileType={fileType}
          onComplete={(success) => {
            setProcessing(false)
            if (success) {
              // Redirect to book page after successful processing
              setTimeout(() => {
                router.push(`/books/${formData.slug}`)
              }, 2000)
            }
          }}
        />

      </div>
    </div>
  )
}
