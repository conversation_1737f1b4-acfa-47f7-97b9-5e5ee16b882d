'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface AudioPlayerProps {
  audioUrl: string
  duration: number

  className?: string
  waveformData?: number[] // Optional pre-computed waveform
  postId?: string // For tracking plays
  onPlayCountUpdate?: (count: number) => void
}

export function AudioPlayer({
  audioUrl,
  duration,

  className = '',
  waveformData,
  postId,
  onPlayCountUpdate
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const animationRef = useRef<number | null>(null)

  // Generate simple waveform bars if no data provided - use fixed length to avoid hydration issues
  const waveform = waveformData || Array.from({ length: 30 }, (_, i) => {
    // Use a deterministic pattern based on index to avoid hydration mismatch
    const pattern = [0.3, 0.7, 0.5, 0.9, 0.4, 0.8, 0.6, 0.2, 0.9, 0.5]
    return pattern[i % pattern.length]
  })

  useEffect(() => {
    // Don't create audio element if no URL provided
    if (!audioUrl || audioUrl.trim() === '') {
      console.warn('AudioPlayer: No audio URL provided')
      return
    }

    console.log('AudioPlayer: Setting up audio for URL:', audioUrl)

    // Validate URL format
    try {
      new URL(audioUrl)
    } catch (urlError) {
      console.error('Invalid audio URL format:', audioUrl, urlError)
      setIsLoading(false)
      return
    }

    // Create audio element
    audioRef.current = new Audio(audioUrl)
    const audio = audioRef.current

    // Ensure volume is set to maximum
    audio.volume = 1.0

    audio.addEventListener('loadstart', () => {
      console.log('Audio loadstart')
      setIsLoading(true)
    })
    audio.addEventListener('canplay', () => {
      console.log('Audio canplay')
      setIsLoading(false)
    })
    audio.addEventListener('error', async (e) => {
      // Only log actual errors, not normal events
      if (e.type === 'error' && audio.error) {
        console.error('Audio playback error:', audio.error.message, 'URL:', audioUrl)

        // Test if the URL is accessible
        try {
          const response = await fetch(audioUrl, { method: 'HEAD' })
          console.log('URL accessibility test:', {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          })
        } catch (fetchError) {
          console.error('URL fetch test failed:', fetchError)
        }
      }
      setIsLoading(false)
    })
    audio.addEventListener('loadeddata', () => {
      console.log('Audio loadeddata')
    })
    audio.addEventListener('timeupdate', () => {
      setCurrentTime(audio.currentTime)
    })
    audio.addEventListener('ended', () => {
      setIsPlaying(false)
      setCurrentTime(0)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    })



    return () => {
      if (audio) {
        audio.pause()
        audio.src = ''
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [audioUrl])

  const setupAudioContext = async () => {
    if (!audioRef.current || audioContextRef.current) return

    try {
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaElementSource(audioRef.current)
      source.connect(analyserRef.current)
      analyserRef.current.connect(audioContextRef.current.destination)
      analyserRef.current.fftSize = 256
    } catch (error) {
      console.error('Error setting up audio context:', error)
    }
  }

  const updateAudioLevel = () => {
    if (!analyserRef.current) return

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount)
    analyserRef.current.getByteFrequencyData(dataArray)
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length
    setAudioLevel(average / 255)

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(updateAudioLevel)
    }
  }

  const handlePlay = async () => {
    if (!audioRef.current || !audioUrl || audioUrl.trim() === '') {
      console.warn('AudioPlayer: Cannot play - no audio element or URL')
      return
    }

    try {
      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
        }
      } else {
        // Track play if postId is provided
        if (postId) {
          try {
            const response = await fetch(`/api/audio/posts/${postId}/play`, {
              method: 'POST'
            })
            if (response.ok) {
              const { play_count } = await response.json()
              onPlayCountUpdate?.(play_count)
            }
          } catch (error) {
            console.error('Failed to track audio play:', error)
          }
        }

        // Simple play without Web Audio API
        await audioRef.current.play()
        setIsPlaying(true)
        // updateAudioLevel() // Disable for now to test
      }
    } catch (error) {
      console.error('Error playing audio:', error)
      setIsPlaying(false)
    }
  }

  const handleSeek = (clickX: number, elementWidth: number) => {
    if (!audioRef.current) return
    
    const seekTime = (clickX / elementWidth) * duration
    audioRef.current.currentTime = seekTime
    setCurrentTime(seekTime)
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={`bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-50 rounded-2xl p-3 sm:p-4 md:p-6 border border-blue-100/50 shadow-inner ${className}`}>
      <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
        {/* Sleek Play/Pause Button */}
        <button
          onClick={handlePlay}
          disabled={isLoading}
          className="group relative w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 flex-shrink-0 rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 text-white flex items-center justify-center p-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden"
        >
          {/* Animated background glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-400 via-purple-400 to-pink-400 rounded-full blur-md opacity-60 group-hover:opacity-80 transition-opacity duration-300 -z-10" />

          {/* Inner circle for depth */}
          <div className="absolute inset-1 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-full" />

          {/* Button content */}
          <div className="relative z-10 flex items-center justify-center">
            {isLoading ? (
              <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white/80 border-t-white rounded-full animate-spin" />
            ) : isPlaying ? (
              // Pause icon - two bars
              <div className="flex items-center gap-0.5 sm:gap-1">
                <div className="w-1 sm:w-1.5 h-3 sm:h-4 bg-white rounded-full" />
                <div className="w-1 sm:w-1.5 h-3 sm:h-4 bg-white rounded-full" />
              </div>
            ) : (
              // Play icon - triangle
              <div className="w-0 h-0 border-l-[6px] sm:border-l-[8px] border-l-white border-t-[4px] sm:border-t-[6px] border-t-transparent border-b-[4px] sm:border-b-[6px] border-b-transparent ml-0.5 sm:ml-1" />
            )}
          </div>

          {/* Ripple effect on click */}
          <div className="absolute inset-0 rounded-full bg-white/20 scale-0 group-active:scale-100 transition-transform duration-150" />
        </button>

        {/* Waveform Visualization */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <div
            className="flex items-center justify-between h-6 sm:h-8 cursor-pointer overflow-hidden w-full"
            onClick={(e) => {
              const rect = e.currentTarget.getBoundingClientRect()
              const clickX = e.clientX - rect.left
              handleSeek(clickX, rect.width)
            }}
          >
            {waveform.map((height, index) => {
              const barProgress = (index / waveform.length) * 100
              const isPassed = barProgress <= progress
              const isActive = isPlaying && Math.abs(barProgress - progress) < 2.5

              return (
                <div
                  key={index}
                  className={`flex-1 max-w-[4px] sm:max-w-[6px] mx-[1px] rounded-full transition-all duration-100 ${
                    isPassed
                      ? 'bg-blue-500'
                      : 'bg-gray-300'
                  } ${
                    isActive ? 'bg-blue-600' : ''
                  }`}
                  style={{
                    height: `${Math.max(3, height * 20)}px`,
                  }}
                />
              )
            })}
          </div>

          {/* Progress indicator */}
          <div className="flex justify-between text-xs sm:text-sm text-gray-500 mt-1 px-1">
            <span>{currentTime.toFixed(1)}s</span>
            <span>{duration.toFixed(1)}s</span>
          </div>
        </div>
      </div>
    </div>
  )
}
