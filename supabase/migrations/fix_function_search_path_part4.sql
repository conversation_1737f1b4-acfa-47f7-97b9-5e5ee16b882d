-- Fix remaining function search_path security warnings
-- Part 4: Final batch of timeline and utility functions

-- Fix create_timeline_post_for_diary function
CREATE OR REPLACE FUNCTION create_timeline_post_for_diary()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Only create timeline post for non-hidden entries
    IF NEW.is_hidden = FALSE THEN
        INSERT INTO public.timeline_posts (
            user_id,
            content_type,
            content_id,
            title,
            preview_text,
            created_at
        ) VALUES (
            NEW.user_id,
            'diary_entry',
            NEW.id,
            NEW.title,
            LEFT(NEW.body_md, 200),
            NEW.created_at
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix create_timeline_post_for_book function
CREATE OR REPLACE FUNCTION create_timeline_post_for_book()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Only create timeline post for public, complete books
    IF NEW.is_private = FALSE AND NEW.is_complete = TRUE THEN
        INSERT INTO public.timeline_posts (
            user_id,
            content_type,
            content_id,
            title,
            preview_text,
            created_at
        ) VALUES (
            NEW.user_id,
            'book_project',
            NEW.id,
            NEW.title,
            LEFT(NEW.description, 200),
            NEW.created_at
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix create_timeline_post_for_voice function
CREATE OR REPLACE FUNCTION create_timeline_post_for_voice()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    INSERT INTO public.timeline_posts (
        user_id,
        content_type,
        content_id,
        title,
        preview_text,
        created_at
    ) VALUES (
        NEW.user_id,
        'audio_post',
        NEW.id,
        COALESCE(NEW.title, 'Voice Post'),
        LEFT(COALESCE(NEW.text_content, ''), 50),
        NEW.created_at
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_view_count function
CREATE OR REPLACE FUNCTION update_view_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update diary entry view count
        IF NEW.diary_entry_id IS NOT NULL THEN
            UPDATE public.diary_entries 
            SET view_count = view_count + 1 
            WHERE id = NEW.diary_entry_id;
        END IF;
        
        -- Update project view count
        IF NEW.project_id IS NOT NULL THEN
            UPDATE public.projects 
            SET view_count = view_count + 1 
            WHERE id = NEW.project_id;
        END IF;
        
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_follower_count function
CREATE OR REPLACE FUNCTION update_follower_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.users 
        SET follower_count = follower_count + 1 
        WHERE id = NEW.following_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.users 
        SET follower_count = GREATEST(follower_count - 1, 0) 
        WHERE id = OLD.following_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix sync_sales_counts function
CREATE OR REPLACE FUNCTION sync_sales_counts()
RETURNS VOID 
SET search_path = ''
AS $$
BEGIN
    -- Update sales counts from purchases table
    UPDATE public.projects 
    SET sales_count = (
        SELECT COUNT(*) 
        FROM public.purchases p 
        WHERE p.project_id = projects.id 
        AND p.status = 'completed'
    );
END;
$$ LANGUAGE plpgsql;

-- Fix update_project_stats function
CREATE OR REPLACE FUNCTION update_project_stats()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.status = 'completed' THEN
        -- Increment sales count
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- Increment sales count when status changes to completed
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' AND OLD.status = 'completed' AND NEW.status != 'completed' THEN
        -- Decrement sales count when status changes from completed
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' AND OLD.status = 'completed' THEN
        -- Decrement sales count when completed purchase is deleted
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = OLD.project_id;
        
        RETURN OLD;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Fix reset_for_launch function
CREATE OR REPLACE FUNCTION reset_for_launch()
RETURNS VOID 
SET search_path = ''
AS $$
BEGIN
    -- Reset all user signup numbers and badges for launch
    UPDATE public.users 
    SET 
        signup_number = NULL,
        has_day1_badge = FALSE,
        badge_tier = NULL;
    
    -- Reset any other launch-related data
    DELETE FROM public.daily_bestsellers;
    
    RAISE NOTICE 'Database reset for launch completed';
END;
$$ LANGUAGE plpgsql;

-- Fix update_project_sales_count function
CREATE OR REPLACE FUNCTION update_project_sales_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = OLD.project_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

SELECT 'Function search_path fixes part 4 applied successfully' as status;
